package com.drex.core.service.business.video.impl;

import com.drex.core.api.response.HeartbeatResponse;
import com.drex.core.dal.tablestore.builder.MaizeRecordBuilder;
import com.drex.core.dal.tablestore.model.MaizeRecord;
import com.drex.core.service.business.video.HeartbeatService;
import com.drex.core.service.config.YouTubeRewardProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 心跳服务实现类
 */
@Slf4j
@Service
public class HeartbeatServiceImpl implements HeartbeatService {

    @Autowired
    private YouTubeRewardProperties youTubeRewardProperties;

    @Autowired
    private MaizeRecordBuilder maizeRecordBuilder;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Override
    public HeartbeatResponse generateHeartbeatResponse(String customerId, String sessionId, 
                                                     String videoId, Double currentPercentage, 
                                                     Integer videoDurationSeconds) {
        try {
            // 计算播放进度信息
            HeartbeatResponse.PlaybackProgress playbackProgress = calculatePlaybackProgress(
                    currentPercentage, videoDurationSeconds);

            // 获取视频奖励状态
            HeartbeatResponse.VideoRewardStatus videoRewardStatus = getVideoRewardStatus(
                    customerId, videoId, sessionId, videoDurationSeconds);

            // 获取X平台奖励状态
            HeartbeatResponse.XPlatformRewardStatus xPlatformRewardStatus = getXPlatformRewardStatus(customerId);

            return HeartbeatResponse.builder()
                    .success(true)
                    .playbackProgress(playbackProgress)
                    .videoRewardStatus(videoRewardStatus)
                    .xPlatformRewardStatus(xPlatformRewardStatus)
                    .serverTimestamp(System.currentTimeMillis())
                    .build();

        } catch (Exception e) {
            log.error("Failed to generate heartbeat response for customer: {}, session: {}", 
                     customerId, sessionId, e);
            
            return HeartbeatResponse.builder()
                    .success(false)
                    .failureReason("Internal server error")
                    .serverTimestamp(System.currentTimeMillis())
                    .build();
        }
    }

    @Override
    public HeartbeatResponse.VideoRewardStatus getVideoRewardStatus(String customerId, String videoId, 
                                                                  String sessionId, Integer videoDurationSeconds) {
        // 判断视频类型
        YouTubeRewardProperties.VideoRewardConfig config = youTubeRewardProperties
                .getVideoRewardConfig(videoDurationSeconds.longValue());
        
        String videoType = videoDurationSeconds <= youTubeRewardProperties.getGlobal().getShortVideoThresholdSeconds() 
                ? "SHORT" : "LONG";

        // 获取所有奖励阶段
        List<HeartbeatResponse.RewardHeartStatus> heartStatuses = new ArrayList<>();
        int completedStages = 0;
        int claimedStages = 0;

        for (YouTubeRewardProperties.ProgressStage stage : config.getStages()) {
            if (!stage.getEnabled()) {
                continue;
            }

            // 检查该阶段的奖励状态
            MaizeRecord existingRecord = maizeRecordBuilder.findMaizeRecordByCompositeKey(
                    customerId, "watch", videoId, sessionId, stage.getStage());

            HeartbeatResponse.HeartColor heartColor;
            String rewardCode = null;
            boolean claimable = false;

            if (existingRecord != null) {
                // 已生成奖励
                if ("COLLECTED".equals(existingRecord.getCollectStatus())) {
                    heartColor = HeartbeatResponse.HeartColor.RED;
                    claimedStages++;
                } else {
                    heartColor = HeartbeatResponse.HeartColor.GRAY;
                    rewardCode = existingRecord.getMaizeCode();
                    claimable = true;
                }
                completedStages++;
            } else {
                // 检查Redis缓存中是否有待领取的奖励
                String cacheKey = String.format("maize:%s:YouTube:watch:%d", customerId, stage.getStage());
                Object cachedReward = redisTemplate.opsForValue().get(cacheKey);
                
                if (cachedReward != null) {
                    heartColor = HeartbeatResponse.HeartColor.GRAY;
                    claimable = true;
                    completedStages++;
                } else {
                    heartColor = HeartbeatResponse.HeartColor.EMPTY;
                }
            }

            HeartbeatResponse.RewardHeartStatus heartStatus = HeartbeatResponse.RewardHeartStatus.builder()
                    .stage(stage.getStage())
                    .stageName(stage.getStageName())
                    .heartColor(heartColor)
                    .rewardAmount(stage.getRewardAmount())
                    .rewardLevel(stage.getRewardLevel())
                    .rewardCode(rewardCode)
                    .conditionDescription(String.format("观看进度达到 %.0f%%-%.0f%%", 
                            stage.getMinWatchPercentage() * 100, stage.getMaxWatchPercentage() * 100))
                    .claimable(claimable)
                    .build();

            heartStatuses.add(heartStatus);
        }

        return HeartbeatResponse.VideoRewardStatus.builder()
                .videoType(videoType)
                .videoId(videoId)
                .heartStatuses(heartStatuses)
                .totalStages(heartStatuses.size())
                .completedStages(completedStages)
                .claimedStages(claimedStages)
                .build();
    }

    @Override
    public HeartbeatResponse.XPlatformRewardStatus getXPlatformRewardStatus(String customerId) {
        YouTubeRewardProperties.XPlatformRewardConfig xConfig = youTubeRewardProperties.getXPlatform();
        
        // 检查发帖奖励状态
        HeartbeatResponse.RewardHeartStatus postRewardStatus = checkXPlatformReward(
                customerId, "post", xConfig.getPostReward());

        // 检查回复奖励状态
        HeartbeatResponse.RewardHeartStatus replyRewardStatus = checkXPlatformReward(
                customerId, "reply", xConfig.getReplyReward());

        // 计算今日奖励次数
        int todayRewardCount = calculateTodayXRewardCount(customerId);

        return HeartbeatResponse.XPlatformRewardStatus.builder()
                .postRewardStatus(postRewardStatus)
                .replyRewardStatus(replyRewardStatus)
                .todayRewardCount(todayRewardCount)
                .maxDailyRewards(Math.max(xConfig.getPostReward().getMaxDailyRewards(),
                                        xConfig.getReplyReward().getMaxDailyRewards()))
                .build();
    }

    @Override
    public HeartbeatResponse.HeartColor calculateHeartColor(String customerId, String videoId, 
                                                          String sessionId, Integer stage, 
                                                          Double currentPercentage, 
                                                          Double stageMinPercentage, 
                                                          Double stageMaxPercentage) {
        // 检查数据库中是否已有记录
        MaizeRecord existingRecord = maizeRecordBuilder.findMaizeRecordByCompositeKey(
                customerId, "watch", videoId, sessionId, stage);

        if (existingRecord != null) {
            return "COLLECTED".equals(existingRecord.getCollectStatus()) 
                    ? HeartbeatResponse.HeartColor.RED 
                    : HeartbeatResponse.HeartColor.GRAY;
        }

        // 检查是否达到条件
        if (currentPercentage >= stageMinPercentage && currentPercentage <= stageMaxPercentage) {
            // 检查Redis缓存
            String cacheKey = String.format("maize:%s:YouTube:watch:%d", customerId, stage);
            Object cachedReward = redisTemplate.opsForValue().get(cacheKey);
            
            return cachedReward != null 
                    ? HeartbeatResponse.HeartColor.GRAY 
                    : HeartbeatResponse.HeartColor.EMPTY;
        }

        return HeartbeatResponse.HeartColor.EMPTY;
    }

    /**
     * 计算播放进度信息
     */
    private HeartbeatResponse.PlaybackProgress calculatePlaybackProgress(Double currentPercentage, 
                                                                       Integer videoDurationSeconds) {
        Integer currentStage = youTubeRewardProperties.calculateProgress(
                currentPercentage, videoDurationSeconds.longValue());

        // 计算下一个阶段的进度要求
        Double nextStagePercentage = calculateNextStagePercentage(currentPercentage, videoDurationSeconds);

        return HeartbeatResponse.PlaybackProgress.builder()
                .currentPercentage(currentPercentage)
                .currentPlaySeconds((int) (currentPercentage * videoDurationSeconds))
                .totalDurationSeconds(videoDurationSeconds)
                .currentStage(currentStage)
                .nextStagePercentage(nextStagePercentage)
                .build();
    }

    /**
     * 计算下一个阶段的进度要求
     */
    private Double calculateNextStagePercentage(Double currentPercentage, Integer videoDurationSeconds) {
        YouTubeRewardProperties.VideoRewardConfig config = youTubeRewardProperties
                .getVideoRewardConfig(videoDurationSeconds.longValue());

        return config.getStages().stream()
                .filter(stage -> stage.getEnabled() && stage.getMinWatchPercentage() > currentPercentage)
                .mapToDouble(YouTubeRewardProperties.ProgressStage::getMinWatchPercentage)
                .min()
                .orElse(1.0);
    }

    /**
     * 检查X平台奖励状态
     */
    private HeartbeatResponse.RewardHeartStatus checkXPlatformReward(String customerId, String eventType, 
                                                                   YouTubeRewardProperties.XRewardStage config) {
        // 检查Redis缓存
        String cacheKey = String.format("maize:%s:X:%s", customerId, eventType);
        Object cachedReward = redisTemplate.opsForValue().get(cacheKey);

        HeartbeatResponse.HeartColor heartColor = cachedReward != null 
                ? HeartbeatResponse.HeartColor.GRAY 
                : HeartbeatResponse.HeartColor.EMPTY;

        return HeartbeatResponse.RewardHeartStatus.builder()
                .stage(1)
                .stageName(eventType.equals("post") ? "发帖奖励" : "回复奖励")
                .heartColor(heartColor)
                .rewardAmount(config.getRewardAmount())
                .rewardLevel(config.getRewardLevel())
                .conditionDescription(eventType.equals("post") ? "发布符合要求的帖子" : "回复符合要求的内容")
                .claimable(cachedReward != null)
                .build();
    }

    /**
     * 计算今日X平台奖励次数
     */
    private int calculateTodayXRewardCount(String customerId) {
        // TODO: 实现今日奖励次数统计逻辑
        // 这里应该查询今日已获得的X平台奖励次数
        return 0;
    }
}
