package com.drex.core.service.business.video;

import com.drex.core.api.response.HeartbeatResponse;

/**
 * 心跳服务接口
 * 处理WebSocket心跳响应，返回奖励状态信息
 */
public interface HeartbeatService {

    /**
     * 生成心跳响应
     * 
     * @param customerId 用户ID
     * @param sessionId 会话ID
     * @param videoId 视频ID
     * @param currentPercentage 当前播放百分比
     * @param videoDurationSeconds 视频时长（秒）
     * @return 心跳响应
     */
    HeartbeatResponse generateHeartbeatResponse(String customerId, String sessionId, 
                                              String videoId, Double currentPercentage, 
                                              Integer videoDurationSeconds);

    /**
     * 获取视频奖励状态
     * 
     * @param customerId 用户ID
     * @param videoId 视频ID
     * @param sessionId 会话ID
     * @param videoDurationSeconds 视频时长
     * @return 视频奖励状态
     */
    HeartbeatResponse.VideoRewardStatus getVideoRewardStatus(String customerId, String videoId, 
                                                           String sessionId, Integer videoDurationSeconds);

    /**
     * 获取X平台奖励状态
     * 
     * @param customerId 用户ID
     * @return X平台奖励状态
     */
    HeartbeatResponse.XPlatformRewardStatus getXPlatformRewardStatus(String customerId);

    /**
     * 计算心的颜色状态
     * 
     * @param customerId 用户ID
     * @param videoId 视频ID
     * @param sessionId 会话ID
     * @param stage 阶段
     * @param currentPercentage 当前播放百分比
     * @param stageMinPercentage 阶段最小百分比
     * @param stageMaxPercentage 阶段最大百分比
     * @return 心的颜色状态
     */
    HeartbeatResponse.HeartColor calculateHeartColor(String customerId, String videoId, 
                                                   String sessionId, Integer stage, 
                                                   Double currentPercentage, 
                                                   Double stageMinPercentage, 
                                                   Double stageMaxPercentage);
}
