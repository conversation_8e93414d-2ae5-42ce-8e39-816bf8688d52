package com.drex.core.service.business.video.impl;

import com.drex.core.api.RemoteVideoAntiCheatService;
import com.drex.core.api.request.VideoReportRequest;
import com.drex.core.api.request.VideoSessionInitRequest;
import com.drex.core.api.response.VideoReportResponse;
import com.drex.core.api.response.VideoSessionInitResponse;
import com.drex.core.dal.tablestore.builder.VideoViewingSessionBuilder;
import com.drex.core.dal.tablestore.model.VideoViewingSession;
import com.drex.core.model.youtube.YouTubeBusinessCode;
import com.drex.core.service.business.video.DataValidationService;
import com.drex.core.service.business.video.RealtimeRewardService;
import com.drex.core.service.business.video.SessionProcessingService;
import com.drex.core.service.cache.model.SessionEvent;
import com.drex.core.service.config.YouTubeRewardProperties;
import com.kikitrade.framework.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Arrays;
import java.util.List;

/**
 * YouTube防刷系统Dubbo服务实现
 */
@Slf4j
@DubboService
public class RemoteVideoAntiCheatServiceImpl implements RemoteVideoAntiCheatService {

    @Autowired
    private SessionProcessingService sessionProcessingService;

    @Autowired
    private DataValidationService dataValidationService;

    @Autowired
    private RealtimeRewardService realtimeRewardService;

    @Autowired
    private VideoViewingSessionBuilder videoViewingSessionBuilder;

    @Autowired
    private YouTubeRewardProperties youTubeRewardProperties;

    @Override
    public Response<VideoSessionInitResponse> initVideoSession(VideoSessionInitRequest request) {
        try {
            log.info("Initializing video session for user: {}, video: {}", 
                    request.getCustomerId(), request.getVideoId());

            // 调用会话处理服务
            VideoSessionInitResponse response = sessionProcessingService.initializeSession(request);

            if (response.getSuccess()) {
                return Response.success(response);
            } else {
                return Response.error(YouTubeBusinessCode.USER_BLACKLISTED.name(), response.getFailureReason());
            }

        } catch (Exception e) {
            log.error("Failed to initialize video session", e);
            VideoSessionInitResponse errorResponse = VideoSessionInitResponse.builder()
                    .success(false)
                    .failureReason("Internal server error")
                    .build();
            return Response.error(YouTubeBusinessCode.UNKNOWN_ERROR.name(), "Internal server error");
        }
    }

    @Override
    public Response<VideoReportResponse> reportEvents(VideoReportRequest request) {
        try {
            log.info("Processing event report for session: {}, events count: {}", 
                    request.getSessionId(), request.getEvents().size());

            // 1. 数据验证
            DataValidationService.ValidationResult validationResult = 
                    dataValidationService.validateReportRequest(request);
            
            if (!validationResult.isValid()) {
                return Response.error(YouTubeBusinessCode.DATA_VALIDATION_FAILED.name(),
                        validationResult.getErrorMessage());
            }

            // 2. 解密和验证事件数据
            List<SessionEvent> events = dataValidationService.validateAndDecryptEvents(
                    request.getEncryptedData(), request.getSignature(),
                    //TODO: 这里应该使用缓存中的密钥
                    sessionProcessingService.generateSessionKey(request.getSessionId(), request.getCustomerId()));

            if (events.isEmpty()) {
                VideoReportResponse errorResponse = VideoReportResponse.builder()
                        .success(false)
                        .failureReason("Failed to decrypt or validate events")
                        .build();
                return Response.error(YouTubeBusinessCode.DECRYPTION_FAILED.name(),
                        "Failed to decrypt events");
            }

            // 3. 更新会话
            SessionProcessingService.SessionUpdateResult updateResult = 
                    sessionProcessingService.updateSession(request.getSessionId(), events);

            if (!updateResult.isSuccess()) {
                VideoReportResponse errorResponse = VideoReportResponse.builder()
                        .success(false)
                        .failureReason(updateResult.getErrorMessage())
                        .build();
                return Response.error(YouTubeBusinessCode.SESSION_EXPIRED.name(),
                        updateResult.getErrorMessage());
            }

            // 4. 实时奖励处理
            RealtimeRewardService.RealtimeProcessingResult rewardResult = processRealtimeReward(
                    request, events, updateResult);

            // 5. 构建响应
            VideoReportResponse response = buildReportResponse(updateResult, request.getSessionId(), rewardResult);
            return Response.success(response);

        } catch (Exception e) {
            log.error("Failed to process event report", e);
            VideoReportResponse errorResponse = VideoReportResponse.builder()
                    .success(false)
                    .failureReason("Internal server error")
                    .build();
            return Response.error(YouTubeBusinessCode.UNKNOWN_ERROR.name(), "Internal server error");
        }
    }


    /**
     * 处理实时奖励
     */
    private RealtimeRewardService.RealtimeProcessingResult processRealtimeReward(
            VideoReportRequest request, List<SessionEvent> events,
            SessionProcessingService.SessionUpdateResult updateResult) {
        try {
            // 从会话信息中获取视频时长
            VideoViewingSession session = videoViewingSessionBuilder.getBySessionId(request.getSessionId());

            // 调用实时奖励服务
            return realtimeRewardService.processRealtimeEvents(
                    request.getSessionId(),
                    request.getCustomerId(),
                    session.getVideoId(),
                    events,
                    session.getVideoDurationSeconds(),
                    request.getSocialPlatform(),
                    request.getSocialEvent()
            );
        } catch (Exception e) {
            log.error("Failed to process realtime reward for session: {}", request.getSessionId(), e);
            return new RealtimeRewardService.RealtimeProcessingResult(false, "Reward processing failed");
        }
    }

    private VideoReportResponse buildReportResponse(SessionProcessingService.SessionUpdateResult updateResult,
                                                    String sessionId,
                                                    RealtimeRewardService.RealtimeProcessingResult realtimeResult) {

        VideoReportResponse.VideoReportResponseBuilder responseBuilder = VideoReportResponse.builder();
        responseBuilder.success(true);

        int effectiveWatchSeconds = realtimeResult.getEffectiveWatchSeconds();
        int videoDurationSeconds = getVideoDurationFromSession(sessionId);
        double watchPercentage = videoDurationSeconds > 0 ?
                (double) effectiveWatchSeconds / videoDurationSeconds : 0.0;


// 计算不同阶段的观看时长要求
        int stage1RequiredSeconds = (int) (videoDurationSeconds * youTubeRewardProperties.getProgress1().getMaxWatchPercentage());
        int stage2RequiredSeconds = (int) (videoDurationSeconds * youTubeRewardProperties.getProgress2().getMaxWatchPercentage());

// 使用原生List存储阶段要求
        List<Integer> stageThresholds = Arrays.asList(stage1RequiredSeconds, stage2RequiredSeconds);

// 计算播放进度
        int playProgress = calculatePlayProgress(effectiveWatchSeconds, stageThresholds);
        responseBuilder.playProgress(playProgress);

// 添加奖励信息        // 添加奖励信息
        if (realtimeResult != null && realtimeResult.getRewardInfo() != null) {
            var rewardInfo = realtimeResult.getRewardInfo();
            responseBuilder.rewardInfo(VideoReportResponse.RewardInfo.builder()
                    .rewardCode(rewardInfo.getRewardCode())
                    .rewardAmount(rewardInfo.getRewardAmount())
                    .rewardLevel(rewardInfo.getRewardLevel())
                    .progress(rewardInfo.getProgress())
                    .build());
        }

        return responseBuilder.build();
    }

    private int calculatePlayProgress(int effectiveWatchSeconds, List<Integer> stageThresholds) {
        if (stageThresholds == null || stageThresholds.isEmpty()) {
            return 1;
        }

        int firstStageMax = stageThresholds.get(0);
        int secondStageMax = stageThresholds.size() > 1 ? stageThresholds.get(1) : Integer.MAX_VALUE;

        // 计算播放进度
        if (effectiveWatchSeconds > secondStageMax) {
            return 3;  // 超过第二阶段
        } else if (effectiveWatchSeconds > firstStageMax) {
            return 2;  // 超过第一阶段
        } else {
            return 1;  // 第一阶段
        }
    }

    /**
     * 从会话中获取视频时长（需要根据实际实现调整）
     */
    private int getVideoDurationFromSession(String sessionId) {
        try {
            VideoViewingSession session = videoViewingSessionBuilder.getBySessionId(sessionId);
            return session.getVideoDurationSeconds();
        } catch (Exception e) {
            log.warn("Failed to get video duration for session: {}, using default", sessionId);
            return 300;
        }
    }

    /**
     * 从会话中获取视频ID（需要根据实际实现调整）
     */
    private String getVideoIdFromSession(String sessionId) {
        try {
            // 这里应该从会话缓存或数据库中获取视频ID
            // 暂时返回默认值
            return "default-video-id";
        } catch (Exception e) {
            log.warn("Failed to get video ID for session: {}, using default", sessionId);
            return "default-video-id";
        }
    }

}
