package com.drex.core.service.business.video.worker;

import com.drex.core.api.request.GenerateMaizeRequest;
import com.drex.core.api.request.SocialConstant;
import com.drex.core.api.request.SocialEventBody;
import com.drex.core.api.response.MaizeDTO;
import com.drex.core.service.business.rexy.MaizeService;
import com.drex.core.service.business.video.RealtimeRewardService;
import com.drex.core.service.config.YouTubeRewardProperties;
import com.drex.core.service.util.async.callback.ICallback;
import com.drex.core.service.util.async.callback.IWorker;
import com.drex.core.service.util.async.worker.WorkResult;
import com.drex.core.service.util.async.wrapper.WorkerWrapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Random;

/**
 * 奖励计算Worker
 * 负责生成奖励代码和积分
 */
@Slf4j
@Component
public class RewardCalculationWorker implements IWorker<RewardCalculationWorker.RewardCalculationParam, RealtimeRewardService.RewardGenerationResult>,
        ICallback<RewardCalculationWorker.RewardCalculationParam, RealtimeRewardService.RewardGenerationResult> {


    @Autowired
    private YouTubeRewardProperties youTubeRewardProperties;

    @Resource
    private MaizeService maizeService;

    @Override
    public RealtimeRewardService.RewardGenerationResult action(RewardCalculationParam param, 
                                                              Map<String, WorkerWrapper> allWrappers) {
        try {
            log.debug("Calculating reward for session: {}, progress: {}, riskScore: {}", 
                    param.getSessionId(), param.getProgress(), param.getRiskScore());

            // 1. 获取奖励规则配置
            YouTubeRewardProperties.ProgressStage progressStage = youTubeRewardProperties.getProgressStage(
                    param.getProgress(), param.getVideoDurationSeconds());
            if (progressStage == null) {
                return new RealtimeRewardService.RewardGenerationResult(false,
                        "Progress stage not found for progress: " + param.getProgress());
            }

            // 2. 检查风险分数要求（风险分数越高，越不应该给奖励）
            // 使用统一的风险分数阈值，超过阈值不发放奖励
            Integer riskScoreThreshold = youTubeRewardProperties.getGlobal().getRiskScoreThreshold();
            if (param.getRiskScore() > riskScoreThreshold) {
                return new RealtimeRewardService.RewardGenerationResult(false,
                        String.format("Risk score too high: %.1f > %d", param.getRiskScore(), riskScoreThreshold));
            }

            // 3. 构建生成玉米请求，包含新的字段
            GenerateMaizeRequest generateRequest = GenerateMaizeRequest.builder()
                    .customerId(param.getCustomerId())
                    .socialPlatform(param.getSocialPlatform())
                    .socialEvent(param.getSocialEvent())
                    .socialEventBody(SocialEventBody.builder()
                            .socialContentId(param.getVideoId())
                            .videoDuration(param.getVideoDurationSeconds())
                            .watchedDuration((long) (param.getWatchPercentage() * param.getVideoDurationSeconds()))
                            .build())
                    .progress(param.getProgress())
                    .sessionId(param.getSessionId())
                    .videoDurationSeconds(param.getVideoDurationSeconds())
                    .watchPercentage(param.getWatchPercentage())
                    .build();

            MaizeDTO maizeDTO = maizeService.generateMaize(generateRequest);

            // 4. 构建返回结果
            RealtimeRewardService.RewardGenerationResult result = new RealtimeRewardService.RewardGenerationResult(
                    true, "Reward generated successfully");
            result.setRewardCode(maizeDTO.getCode());
            result.setRewardAmount(progressStage.getRewardAmount());
            result.setRewardLevel(progressStage.getRewardLevel());
            result.setExpirationTime(System.currentTimeMillis() + 24 * 60 * 60 * 1000L); // 24小时过期

            log.info("Reward generated successfully for session: {}, progress: {}, code: {}, score: {}", 
                    param.getSessionId(), param.getProgress(), maizeDTO.getCode(), maizeDTO.getScore());

            return result;

        } catch (Exception e) {
            log.error("Failed to generate reward for session: {}, progress: {}", param.getSessionId(), param.getProgress(), e);
            return new RealtimeRewardService.RewardGenerationResult(false, "Failed to generate reward: " + e.getMessage());
        }
    }

    @Override
    public void begin() {
        log.debug("RewardCalculationWorker begin");
    }

    @Override
    public void result(boolean success, RewardCalculationParam param, WorkResult<RealtimeRewardService.RewardGenerationResult> result) {
        if (success) {
            log.debug("RewardCalculationWorker completed successfully for session: {}", param.getSessionId());
        } else {
            log.error("RewardCalculationWorker failed for session: {}", param.getSessionId());
        }
    }


    /**
     * 奖励计算参数
     */
    public static class RewardCalculationParam {
        private final String sessionId;
        private final String customerId;
        private final String videoId;
        private final Integer progress;
        private final double riskScore;
        private final SocialConstant.PlatformEnum socialPlatform;
        private final SocialConstant.EventEnum socialEvent;
        private final Long videoDurationSeconds;
        private final Double watchPercentage;

        public RewardCalculationParam(String sessionId, String customerId, String videoId, Integer progress,
                                    double riskScore, SocialConstant.PlatformEnum socialPlatform,
                                    SocialConstant.EventEnum socialEvent, Long videoDurationSeconds,
                                    Double watchPercentage) {
            this.sessionId = sessionId;
            this.customerId = customerId;
            this.videoId = videoId;
            this.progress = progress;
            this.riskScore = riskScore;
            this.socialPlatform = socialPlatform;
            this.socialEvent = socialEvent;
            this.videoDurationSeconds = videoDurationSeconds;
            this.watchPercentage = watchPercentage;
        }

        public String getSessionId() { return sessionId; }
        public String getCustomerId() { return customerId; }
        public String getVideoId() { return videoId; }
        public Integer getProgress() { return progress; }
        public double getRiskScore() { return riskScore; }
        public SocialConstant.PlatformEnum getSocialPlatform() { return socialPlatform; }
        public SocialConstant.EventEnum getSocialEvent() { return socialEvent; }
        public Long getVideoDurationSeconds() { return videoDurationSeconds; }
        public Double getWatchPercentage() { return watchPercentage; }
    }
}
