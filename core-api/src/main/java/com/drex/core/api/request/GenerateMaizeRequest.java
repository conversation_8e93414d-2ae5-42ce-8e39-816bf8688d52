package com.drex.core.api.request;

import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

@Data
@Builder
public class GenerateMaizeRequest implements Serializable {

    private String customerId;
    private SocialConstant.PlatformEnum socialPlatform;
    private SocialConstant.EventEnum socialEvent;
    private SocialEventBody socialEventBody;

    /**
     * 奖励进度阶段（用于YouTube视频观看奖励）
     */
    private Integer progress;

    /**
     * 会话ID（用于关联观看会话）
     */
    private String sessionId;

    /**
     * 视频时长（秒）
     */
    private Long videoDurationSeconds;

    /**
     * 观看百分比
     */
    private Double watchPercentage;
}
