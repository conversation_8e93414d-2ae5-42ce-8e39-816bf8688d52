package com.drex.core.service.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * YouTube奖励配置属性类
 * 从application.properties读取配置
 * 支持长视频、短视频、X平台互动的差异化奖励配置
 */
@Data
@Component
@ConfigurationProperties(prefix = "youtube.reward")
public class YouTubeRewardProperties {

    /**
     * 长视频奖励配置
     */
    private VideoRewardConfig longVideo = new VideoRewardConfig();

    /**
     * 短视频奖励配置
     */
    private VideoRewardConfig shortVideo = new VideoRewardConfig();

    /**
     * X平台互动奖励配置
     */
    private XPlatformRewardConfig xPlatform = new XPlatformRewardConfig();

    /**
     * 全局配置
     */
    private GlobalConfig global = new GlobalConfig();

    /**
     * 进度配置
     */
    @Data
    public static class ProgressConfig {
        /**
         * 最小观看百分比
         */
        private Double minWatchPercentage = 0.0;

        /**
         * 最大观看百分比
         */
        private Double maxWatchPercentage = 1.0;

        /**
         * 奖励金额
         */
        private Long rewardAmount = 0L;

        private String rewardLevel = "GOLD";
    }

    /**
     * 全局配置
     */
    @Data
    public static class GlobalConfig {
        /**
         * 最小会话时长（秒）
         */
        private Integer minSessionDurationSeconds = 60;

        /**
         * 最大会话时长（秒）
         */
        private Integer maxSessionDurationSeconds = 7200;

        /**
         * 是否启用实时奖励
         */
        private Boolean enableRealtimeReward = true;

        /**
         * 心跳间隔（秒）
         */
        private Integer heartbeatIntervalSeconds = 30;

        /**
         * 事件上报间隔（秒）
         */
        private Integer eventReportIntervalSeconds = 10;

        /**
         * 最大并发会话数
         */
        private Integer maxConcurrentSessions = 5;

        /**
         * 风险分数阈值（分数越高风险越高，超过阈值不发放奖励）
         */
        private Integer riskScoreThreshold = 95;
    }
    /**
     * 根据进度等级获取配置
     */
    public ProgressConfig getProgressConfig(Integer progress) {
        switch (progress) {
            case 1:
                return progress1;
            case 2:
                return progress2;
            case 3:
                return progress3;
            default:
                return null;
        }
    }

    /**
     * 根据观看百分比计算进度等级
     */
    public Integer calculateProgress(Double watchPercentage) {
        if (watchPercentage == null) {
            return null;
        }

        // 检查进度1
        if (
                watchPercentage >= progress1.getMinWatchPercentage() &&
                watchPercentage <= progress1.getMaxWatchPercentage()) {
            return 1;
        }

        // 检查进度2
        if (
                watchPercentage >= progress2.getMinWatchPercentage() &&
                watchPercentage <= progress2.getMaxWatchPercentage()) {
            return 2;
        }

        // 检查进度3
        if (
                watchPercentage >= progress3.getMinWatchPercentage() &&
                watchPercentage <= progress3.getMaxWatchPercentage()) {
            return 3;
        }

        return null;
    }


        /**
         * 验证配置有效性
         */
    public boolean isValid() {
        return validateProgressConfig(progress1) && 
               validateProgressConfig(progress2) && 
               validateProgressConfig(progress3);
    }

    private boolean validateProgressConfig(ProgressConfig config) {
        return config.getMinWatchPercentage() != null &&
               config.getMaxWatchPercentage() != null &&
               config.getMinWatchPercentage() <= config.getMaxWatchPercentage();
    }
}
