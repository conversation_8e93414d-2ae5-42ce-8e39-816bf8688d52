package com.drex.core.web.controller;

import com.drex.core.api.response.HeartbeatResponse;
import com.drex.core.service.business.video.HeartbeatService;
import com.kikitrade.framework.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 心跳响应控制器
 * 提供WebSocket心跳响应数据
 */
@Slf4j
@RestController
@RequestMapping("/api/heartbeat")
public class HeartbeatController {

    @Autowired
    private HeartbeatService heartbeatService;

    /**
     * 获取心跳响应数据
     * 
     * @param customerId 用户ID
     * @param sessionId 会话ID
     * @param videoId 视频ID
     * @param currentPercentage 当前播放百分比
     * @param videoDurationSeconds 视频时长（秒）
     * @return 心跳响应
     */
    @GetMapping("/status")
    public Response<HeartbeatResponse> getHeartbeatStatus(
            @RequestParam String customerId,
            @RequestParam String sessionId,
            @RequestParam String videoId,
            @RequestParam Double currentPercentage,
            @RequestParam Integer videoDurationSeconds) {
        
        try {
            log.debug("Getting heartbeat status for customer: {}, session: {}, video: {}", 
                     customerId, sessionId, videoId);
            
            HeartbeatResponse response = heartbeatService.generateHeartbeatResponse(
                    customerId, sessionId, videoId, currentPercentage, videoDurationSeconds);
            
            return Response.success(response);
            
        } catch (Exception e) {
            log.error("Failed to get heartbeat status for customer: {}, session: {}", 
                     customerId, sessionId, e);
            
            HeartbeatResponse errorResponse = HeartbeatResponse.builder()
                    .success(false)
                    .failureReason("Internal server error")
                    .serverTimestamp(System.currentTimeMillis())
                    .build();
            
            return Response.success(errorResponse);
        }
    }

    /**
     * 获取视频奖励状态
     * 
     * @param customerId 用户ID
     * @param videoId 视频ID
     * @param sessionId 会话ID
     * @param videoDurationSeconds 视频时长
     * @return 视频奖励状态
     */
    @GetMapping("/video-reward-status")
    public Response<HeartbeatResponse.VideoRewardStatus> getVideoRewardStatus(
            @RequestParam String customerId,
            @RequestParam String videoId,
            @RequestParam String sessionId,
            @RequestParam Integer videoDurationSeconds) {
        
        try {
            HeartbeatResponse.VideoRewardStatus status = heartbeatService.getVideoRewardStatus(
                    customerId, videoId, sessionId, videoDurationSeconds);
            
            return Response.success(status);
            
        } catch (Exception e) {
            log.error("Failed to get video reward status for customer: {}, video: {}", 
                     customerId, videoId, e);
            return Response.error("9999", "Failed to get video reward status");
        }
    }

    /**
     * 获取X平台奖励状态
     * 
     * @param customerId 用户ID
     * @return X平台奖励状态
     */
    @GetMapping("/x-platform-reward-status")
    public Response<HeartbeatResponse.XPlatformRewardStatus> getXPlatformRewardStatus(
            @RequestParam String customerId) {
        
        try {
            HeartbeatResponse.XPlatformRewardStatus status = heartbeatService.getXPlatformRewardStatus(customerId);
            
            return Response.success(status);
            
        } catch (Exception e) {
            log.error("Failed to get X platform reward status for customer: {}", customerId, e);
            return Response.error("9999", "Failed to get X platform reward status");
        }
    }
}
