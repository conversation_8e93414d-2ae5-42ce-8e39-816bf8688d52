package com.drex.core.api.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * WebSocket心跳响应
 * 统一展示长视频、短视频、X平台互动的奖励状态
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class HeartbeatResponse implements Serializable {

    /**
     * 是否成功
     */
    private Boolean success;

    /**
     * 失败原因
     */
    private String failureReason;

    /**
     * 当前播放进度信息
     */
    private PlaybackProgress playbackProgress;

    /**
     * 视频奖励状态
     */
    private VideoRewardStatus videoRewardStatus;

    /**
     * X平台互动奖励状态
     */
    private XPlatformRewardStatus xPlatformRewardStatus;

    /**
     * 服务器时间戳
     */
    private Long serverTimestamp;

    /**
     * 播放进度信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PlaybackProgress implements Serializable {
        /**
         * 当前播放百分比
         */
        private Double currentPercentage;

        /**
         * 当前播放时长（秒）
         */
        private Integer currentPlaySeconds;

        /**
         * 视频总时长（秒）
         */
        private Integer totalDurationSeconds;

        /**
         * 当前所在奖励阶段
         */
        private Integer currentStage;

        /**
         * 下一个奖励阶段的进度要求
         */
        private Double nextStagePercentage;
    }

    /**
     * 视频奖励状态
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class VideoRewardStatus implements Serializable {
        /**
         * 视频类型（LONG/SHORT）
         */
        private String videoType;

        /**
         * 视频ID
         */
        private String videoId;

        /**
         * 奖励心的状态列表
         */
        private List<RewardHeartStatus> heartStatuses;

        /**
         * 总奖励阶段数
         */
        private Integer totalStages;

        /**
         * 已完成阶段数
         */
        private Integer completedStages;

        /**
         * 已领取阶段数
         */
        private Integer claimedStages;
    }

    /**
     * X平台奖励状态
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class XPlatformRewardStatus implements Serializable {
        /**
         * 发帖奖励状态
         */
        private RewardHeartStatus postRewardStatus;

        /**
         * 回复奖励状态
         */
        private RewardHeartStatus replyRewardStatus;

        /**
         * 今日已获得奖励次数
         */
        private Integer todayRewardCount;

        /**
         * 今日最大奖励次数
         */
        private Integer maxDailyRewards;
    }

    /**
     * 奖励心状态
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RewardHeartStatus implements Serializable {
        /**
         * 阶段编号
         */
        private Integer stage;

        /**
         * 阶段名称
         */
        private String stageName;

        /**
         * 心的颜色状态
         * EMPTY: 空心（尚未达成条件）
         * GRAY: 灰心（已达成但未领取）
         * RED: 红心（已达成且已领取）
         */
        private HeartColor heartColor;

        /**
         * 奖励金额
         */
        private Long rewardAmount;

        /**
         * 奖励等级
         */
        private String rewardLevel;

        /**
         * 奖励代码（如果已生成）
         */
        private String rewardCode;

        /**
         * 达成条件描述
         */
        private String conditionDescription;

        /**
         * 是否可领取
         */
        private Boolean claimable;

        /**
         * 奖励过期时间
         */
        private Long expireTime;
    }

    /**
     * 心的颜色枚举
     */
    public enum HeartColor {
        /**
         * 空心：该阶段奖励尚未达成条件
         */
        EMPTY,

        /**
         * 灰心：该阶段奖励已达成但未领取
         */
        GRAY,

        /**
         * 红心：该阶段奖励已达成且已领取
         */
        RED
    }
}
